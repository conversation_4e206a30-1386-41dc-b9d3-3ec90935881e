"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import Image from "next/image";

type Props = {
  className?: string;
};

type ServiceCategory =
  | "all"
  | "animation"
  | "design"
  | "programming"
  | "marketing"
  | "audiovisual"
  | "3d";

const ServicesSection = ({ className }: Props) => {
  const t = useTranslations("services");
  const [activeCategory, setActiveCategory] = useState<ServiceCategory>("all");

  // Get all service items from translations
  const serviceItems = [
    "design_fenix",
    "design_hellen",
    "design_linha",
    "design_solo",
    "programming_andiamo",
    "programming_azul",
  ];

  const categories: ServiceCategory[] = [
    "all",
    "animation",
    "design",
    "programming",
    "marketing",
    "audiovisual",
    "3d",
  ];

  const filteredServices = useMemo(() => {
    if (activeCategory === "all") {
      return serviceItems;
    }
    return serviceItems.filter((item) => {
      const category = t(`items.${item}.category` as any) as ServiceCategory;
      return category === activeCategory;
    });
  }, [activeCategory, t, serviceItems]);

  return (
    <section className={cn("bg-background py-16", className)}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mx-auto mb-12 max-w-4xl text-center">
          <h2 className="text-foreground mb-6 text-4xl font-bold sm:text-5xl">
            {t("title")}
          </h2>
          <p className="text-muted-foreground text-lg sm:text-xl">
            {t("description")}
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="mb-12 flex flex-wrap justify-center gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={cn(
                "rounded-full px-4 py-2 text-sm font-medium transition-all duration-300 hover:scale-105",
                activeCategory === category
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "bg-muted text-muted-foreground hover:bg-primary/10 hover:text-primary",
              )}
            >
              {t(`categories.${category}`)}
            </button>
          ))}
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {filteredServices.map((item) => (
            <div
              key={item}
              className="group bg-card overflow-hidden rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={`/src/assets/imgs/${t(`items.${item}.image` as any)}`}
                  alt={t(`items.${item}.title` as any)}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <div className="p-6">
                <h3 className="text-card-foreground group-hover:text-primary mb-2 text-xl font-semibold transition-colors">
                  {t(`items.${item}.title` as any)}
                </h3>
                <p className="text-muted-foreground text-base">
                  {t(`items.${item}.description` as any)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
