"use server";

import { getTranslations } from "next-intl/server";
import { ContactFormData, contactFormSchema } from "./contact/model";
import { sendEmail } from "@/lib/mailer";
import { appEnv } from "@/env";
import { z } from "zod/v4";

const contactEmailTemplate = ({ name, email, whatsapp }: ContactFormData) => `
<div style="font-family: Arial, sans-serif; max-width: 500px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2 style="color: #333; margin-top: 0; text-align: center;">Nova Mensagem do Formulário de Contato</h2>

        <div style="margin: 20px 0;">
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>Nome:</strong> ${name}
            </p>
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>Email:</strong> ${email}
            </p>
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>WhatsApp:</strong> ${whatsapp}
            </p>
        </div>

        <p style="text-align: center; color: #666; font-size: 12px; margin-top: 20px;">
            Recebido em ${new Date().toLocaleDateString("pt-BR")}
        </p>
    </div>
</div>
`;

const userSubscriptionConfirmEmailTemplate = ({ name }: ContactFormData) => `
<div style="font-family: Arial, sans-serif; max-width: 500px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2 style="color: #333; margin-top: 0; text-align: center;">Obrigado por se inscrever na nossa newsletter!</h2>

        <p style="margin: 20px 0; color: #555;">
            Olá ${name},<br>
            Agradecemos por se inscrever na nossa newsletter!<br>
            Em breve você receberá novidades e atualizações sobre nossas coleções, eventos e iniciativas.
        </p>

        <p style="margin: 20px 0; color: #555;">
            Caso não tenha se inscrito, por favor ignore este email ou entre em contato conosco através do email ${appEnv.CONTACT_EMAIL}.
        </p>

        <p style="text-align: center; color: #666; font-size: 12px; margin-top: 20px;">
            Rose Palhares © ${new Date().getFullYear()}<br>
        </p>
    </div>
</div>
`;

export const sendContactEmail = async (payload: ContactFormData) => {
  const tValidation = await getTranslations("validation");

  try {
    const data = contactFormSchema(tValidation).parse(payload);

    await sendEmail({
      to: appEnv.CONTACT_EMAIL,
      subject: "🌟 Nova Mensagem de Contato",
      html: contactEmailTemplate(data),
    });

    await sendEmail({
      to: data.email,
      subject: "Obrigado por se inscrever na nossa newsletter!",
      html: userSubscriptionConfirmEmailTemplate(data),
    });

    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error(z.prettifyError(error));
      return { success: false };
    }
    console.error(error);
    return { success: false };
  }
};
