import { getTranslations } from "next-intl/server";
import { cn } from "@/lib/utils";
import ContactForm from "./ContactForm";
import SocialLinks from "../components/SocialLinks";

type Props = {
  className?: string;
};

const ContactSection = async ({ className }: Props) => {
  const t = await getTranslations("contact");

  return (
    <section id="contact" className={cn("bg-ring", className)}>
      <div className="container-full flex gap-8 max-sm:flex-col">
        <ContactForm className="flex-2" />
        <article className="flex flex-1 flex-col gap-8">
          <p className="text-muted leading-tight">{t("consent")}</p>
          <SocialLinks />
        </article>
      </div>
    </section>
  );
};

export default ContactSection;
