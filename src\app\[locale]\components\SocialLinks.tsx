import { getTranslations } from "next-intl/server";
import { Instagram, Linkedin } from "lucide-react";
import WhatsApp from "@/assets/imgs/icons/whatsapp.svg";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const SocialLinks = async ({ className }: Props) => {
  const t = await getTranslations("contact.social");

  const socialLinks = [
    {
      icon: Instagram,
      label: t("instagram"),
      href: "https://instagram.com/rose.palhares.rtw",
      ariaLabel: "Follow us on Instagram",
    },
    {
      icon: Linkedin,
      label: t("linkedin"),
      href: "https://linkedin.com/company/rose-palhares",
      ariaLabel: "Connect with us on LinkedIn",
    },
    {
      icon: WhatsApp,
      label: t("whatsapp"),
      href: "https://wa.me/244942872663",
      ariaLabel: "Contact us on WhatsApp",
    },
  ];

  return (
    <div className={cn("grid gap-6", className)}>
      {socialLinks.map((link, index) => {
        const Icon = link.icon;
        return (
          <a
            key={index}
            href={link.href}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={link.ariaLabel}
            className="text-foreground hover:text-foreground/60 group flex items-center gap-2 transition-colors"
          >
            <Icon
              className="size-6 shrink-0 transition-transform group-hover:scale-110"
              strokeWidth={1.5}
            />
            <span className="font-medium tracking-wide text-nowrap">
              {link.label}
            </span>
          </a>
        );
      })}
    </div>
  );
};

export default SocialLinks;
