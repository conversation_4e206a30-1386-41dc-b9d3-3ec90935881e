import { useTranslations } from "next-intl";
import * as z from "zod/v4-mini";

export const contactFormSchema = (
  t: ReturnType<typeof useTranslations<"validation">>,
) =>
  z.object({
    name: z
      .string()
      .check(
        z.minLength(
          2,
          t("minLengthError", { field: t("nameField"), min: "2" }),
        ),
        z.maxLength(
          50,
          t("maxLengthError", { field: t("nameField"), max: "50" }),
        ),
      ),
    email: z.email(t("emailError")),
    whatsapp: z.string().check(
      z.regex(
        /^\+\d{1,3}\d{9}$/,
        t("regexError", {
          field: t("whatsappField"),
          format: "+(area)(9 digits)",
        }),
      ),
    ),
  });
export type ContactFormData = z.infer<ReturnType<typeof contactFormSchema>>;
