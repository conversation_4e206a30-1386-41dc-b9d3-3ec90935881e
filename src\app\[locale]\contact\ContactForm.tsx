"use client";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { ContactFormData, contactFormSchema } from "./model";
import { useTransition } from "react";
import { sendContactEmail } from "../actions";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

type Props = {
  className?: string;
};

const ContactForm = ({ className }: Props) => {
  const t = useTranslations("contact.form");
  const tValidation = useTranslations("validation");
  const [isPending, startTransition] = useTransition();

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema(tValidation)),
    defaultValues: {
      name: "",
      email: "",
      whatsapp: "",
    },
  });

  const onSubmit = async (data: ContactFormData) =>
    startTransition(async () => {
      const result = await sendContactEmail(data);

      if (!result.success) {
        toast.error(t("error"));
        return;
      }

      toast.success(t("success"));
      form.reset({});
    });

  return (
    <article className={cn("flex w-full max-w-2xl flex-col gap-8", className)}>
      <h2 className="text-xl font-medium tracking-wider uppercase lg:text-2xl">
        {t("title")}
      </h2>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tValidation("nameField")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="John Doe" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="">
                <FormLabel>{tValidation("emailField")}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="whatsapp"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>{tValidation("whatsappField")}</FormLabel>
                <div className="flex gap-6 max-sm:flex-col sm:items-end">
                  <FormControl>
                    <Input {...field} type="tel" placeholder="+244999000000" />
                  </FormControl>
                  <Button
                    type="submit"
                    variant="secondary"
                    size="sm"
                    className="max-sm:hidden"
                    disabled={isPending}
                  >
                    {t("send")}
                    {isPending && (
                      <Loader2 className="mb-0.5 size-4 animate-spin" />
                    )}
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            variant="secondary"
            size="sm"
            className="sm:hidden"
            disabled={isPending}
          >
            {t("send")}
            {isPending && <Loader2 className="mb-0.5 size-4 animate-spin" />}
          </Button>
        </form>
      </Form>
    </article>
  );
};

export default ContactForm;
